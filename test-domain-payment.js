// 测试域名支付功能
const https = require('https');
const http = require('http');

// 测试配置
const testConfig = {
  domain: 'chendumiaomu.asia',
  protocols: ['https', 'http'], // 优先测试HTTPS，失败则尝试HTTP
  endpoints: [
    { path: '/health', method: 'GET', needAuth: false },
    { path: '/api/auth/wechat-login', method: 'POST', needAuth: false },
    { path: '/api/payment/create', method: 'POST', needAuth: true },
    { path: '/api/payment/query/test123', method: 'GET', needAuth: true },
    { path: '/api/payment/verify', method: 'POST', needAuth: true },
    { path: '/api/payment/notify', method: 'POST', needAuth: false }
  ]
};

// 发起HTTP/HTTPS请求
function makeRequest(protocol, hostname, path, method = 'GET', data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const requestModule = protocol === 'https' ? https : http;
    const port = protocol === 'https' ? 443 : 80;
    
    const options = {
      hostname: hostname,
      port: port,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Domain-Payment-Test/1.0',
        ...headers
      },
      timeout: 10000
    };

    const req = requestModule.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: responseData,
          protocol: protocol
        });
      });
    });

    req.on('error', (error) => {
      reject({
        error: error.message,
        protocol: protocol
      });
    });

    req.on('timeout', () => {
      req.destroy();
      reject({
        error: '请求超时',
        protocol: protocol
      });
    });

    if (data && method !== 'GET') {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// 测试单个端点
async function testEndpoint(protocol, domain, endpoint) {
  try {
    const testData = endpoint.needAuth ? 
      { test: true } : 
      (endpoint.path.includes('wechat-login') ? 
        { code: 'test_code', userInfo: { nickName: '测试用户' } } : 
        { test: true });
    
    const headers = endpoint.needAuth ? 
      { 'Authorization': 'Bearer test_token_for_testing' } : 
      {};

    const result = await makeRequest(
      protocol, 
      domain, 
      endpoint.path, 
      endpoint.method, 
      endpoint.method !== 'GET' ? testData : null,
      headers
    );

    return {
      ...endpoint,
      success: true,
      statusCode: result.statusCode,
      protocol: result.protocol,
      accessible: result.statusCode < 500,
      response: result.data.substring(0, 200) // 限制响应长度
    };

  } catch (error) {
    return {
      ...endpoint,
      success: false,
      error: error.error || error.message,
      protocol: protocol,
      accessible: false
    };
  }
}

// 测试域名连通性
async function testDomainConnectivity(domain) {
  console.log(`🔍 测试域名连通性: ${domain}\n`);
  
  const results = {};
  
  for (const protocol of testConfig.protocols) {
    console.log(`📡 测试 ${protocol.toUpperCase()} 协议...`);
    
    try {
      const result = await makeRequest(protocol, domain, '/health', 'GET');
      results[protocol] = {
        success: true,
        statusCode: result.statusCode,
        data: result.data
      };
      console.log(`✅ ${protocol.toUpperCase()} 连接成功 (状态码: ${result.statusCode})`);
      
      // 如果HTTPS成功，就不需要测试HTTP了
      if (protocol === 'https') {
        break;
      }
      
    } catch (error) {
      results[protocol] = {
        success: false,
        error: error.error || error.message
      };
      console.log(`❌ ${protocol.toUpperCase()} 连接失败: ${error.error || error.message}`);
    }
  }
  
  console.log('');
  return results;
}

// 测试所有API端点
async function testAllEndpoints(domain, workingProtocol) {
  console.log(`🧪 测试API端点 (使用 ${workingProtocol.toUpperCase()})...\n`);
  
  const results = [];
  
  for (const endpoint of testConfig.endpoints) {
    console.log(`测试: ${endpoint.method} ${endpoint.path}`);
    
    const result = await testEndpoint(workingProtocol, domain, endpoint);
    results.push(result);
    
    if (result.success) {
      const status = result.accessible ? '✅' : '⚠️';
      console.log(`${status} ${result.statusCode} - ${result.accessible ? '可访问' : '服务器错误'}`);
    } else {
      console.log(`❌ 连接失败 - ${result.error}`);
    }
  }
  
  return results;
}

// 生成测试报告
function generateReport(connectivityResults, endpointResults, domain) {
  console.log('\n📊 测试报告');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  
  // 连通性报告
  console.log('\n🌐 域名连通性:');
  Object.entries(connectivityResults).forEach(([protocol, result]) => {
    const status = result.success ? '✅' : '❌';
    const message = result.success ? 
      `${protocol.toUpperCase()} 正常 (${result.statusCode})` : 
      `${protocol.toUpperCase()} 失败 (${result.error})`;
    console.log(`   ${status} ${message}`);
  });
  
  // 端点测试报告
  if (endpointResults && endpointResults.length > 0) {
    console.log('\n🔗 API端点测试:');
    console.log('端点                              方法    状态码   可访问');
    console.log('─────────────────────────────────────────────────────────');
    
    endpointResults.forEach(result => {
      const path = result.path.padEnd(35);
      const method = result.method.padEnd(6);
      const statusCode = result.success ? result.statusCode.toString().padEnd(7) : 'N/A'.padEnd(7);
      const accessible = result.accessible ? '✅' : '❌';
      
      console.log(`${path} ${method} ${statusCode} ${accessible}`);
      
      if (!result.success && result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
  
  // 总结
  const workingProtocols = Object.entries(connectivityResults)
    .filter(([_, result]) => result.success)
    .map(([protocol, _]) => protocol);
  
  const accessibleEndpoints = endpointResults ? 
    endpointResults.filter(r => r.accessible).length : 0;
  const totalEndpoints = endpointResults ? endpointResults.length : 0;
  
  console.log('\n📋 总结:');
  console.log(`   域名: ${domain}`);
  console.log(`   可用协议: ${workingProtocols.join(', ') || '无'}`);
  if (endpointResults) {
    console.log(`   API端点: ${accessibleEndpoints}/${totalEndpoints} 可访问`);
  }
  
  // 建议
  console.log('\n💡 建议:');
  if (workingProtocols.length === 0) {
    console.log('   ❌ 域名无法访问，请检查:');
    console.log('      1. DNS解析是否正确');
    console.log('      2. 服务器防火墙设置');
    console.log('      3. Nginx是否正确配置');
    console.log('      4. Node.js服务是否运行');
  } else {
    const recommendedProtocol = workingProtocols.includes('https') ? 'https' : 'http';
    console.log(`   ✅ 推荐使用: ${recommendedProtocol}://${domain}`);
    
    if (!workingProtocols.includes('https')) {
      console.log('   ⚠️  建议配置SSL证书以支持HTTPS');
    }
  }
  
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
}

// 主测试函数
async function runDomainPaymentTest() {
  console.log('🚀 开始测试域名支付功能...\n');
  
  try {
    // 1. 测试域名连通性
    const connectivityResults = await testDomainConnectivity(testConfig.domain);
    
    // 2. 确定可用的协议
    const workingProtocol = connectivityResults.https?.success ? 'https' : 
                           connectivityResults.http?.success ? 'http' : null;
    
    let endpointResults = null;
    
    if (workingProtocol) {
      // 3. 测试API端点
      endpointResults = await testAllEndpoints(testConfig.domain, workingProtocol);
    } else {
      console.log('❌ 域名无法访问，跳过API端点测试\n');
    }
    
    // 4. 生成报告
    generateReport(connectivityResults, endpointResults, testConfig.domain);
    
    // 5. 返回推荐配置
    if (workingProtocol) {
      const recommendedUrl = `${workingProtocol}://${testConfig.domain}`;
      console.log(`🎯 推荐在 nodePaymentConfig.js 中使用:`);
      console.log(`   const apiUrl = '${recommendedUrl}';`);
      
      return {
        success: true,
        recommendedUrl: recommendedUrl,
        protocol: workingProtocol
      };
    } else {
      console.log(`🔧 当前建议继续使用IP地址:`);
      console.log(`   const apiUrl = 'http://***********:3002';`);
      
      return {
        success: false,
        fallbackUrl: 'http://***********:3002'
      };
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    return { success: false, error: error.message };
  }
}

// 运行测试
if (require.main === module) {
  runDomainPaymentTest()
    .then(result => {
      if (result.success) {
        console.log('🎉 域名访问测试完成！');
      } else {
        console.log('⚠️  域名访问需要进一步配置');
      }
    })
    .catch(console.error);
}

module.exports = {
  runDomainPaymentTest,
  testDomainConnectivity,
  testAllEndpoints
};
