// 调试域名请求工具
const https = require('https');

// 测试配置
const testConfig = {
  domain: 'chendumiaomu.asia',
  testData: {
    code: "0f10PNkl2YIE3g4BTxol2vEJyu20PNkn",
    userInfo: {
      nickName: "Riki23",
      avatarUrl: "https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTKxrUx0mxFicrwUibzRDLvEXrGXVgMHwjNVcuXe6QqWa56cYadR8iadO9qBDK4QWeQRnDvhwmSBcCcaw/132",
      gender: 1,
      country: "中国",
      province: "广东",
      city: "深圳"
    }
  }
};

// 发起HTTPS请求
function makeHttpsRequest(hostname, path, method = 'POST', data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const postData = data ? JSON.stringify(data) : '';
    
    const options = {
      hostname: hostname,
      port: 443,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Domain-Debug-Tool/1.0',
        'Content-Length': Buffer.byteLength(postData),
        ...headers
      },
      timeout: 15000
    };

    console.log('🔍 发起请求:', {
      url: `https://${hostname}${path}`,
      method: method,
      headers: options.headers,
      data: data
    });

    const req = https.request(options, (res) => {
      let responseData = '';
      
      console.log('📡 响应状态:', res.statusCode);
      console.log('📋 响应头:', res.headers);
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = responseData ? JSON.parse(responseData) : null;
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsedData,
            rawData: responseData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: null,
            rawData: responseData,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ 请求错误:', error);
      reject(error);
    });

    req.on('timeout', () => {
      console.error('⏰ 请求超时');
      req.destroy();
      reject(new Error('请求超时'));
    });

    if (postData) {
      req.write(postData);
    }
    
    req.end();
  });
}

// 测试健康检查
async function testHealthCheck() {
  console.log('🏥 测试健康检查端点...\n');
  
  try {
    const result = await makeHttpsRequest(testConfig.domain, '/health', 'GET');
    
    console.log('✅ 健康检查结果:');
    console.log('   状态码:', result.statusCode);
    console.log('   响应数据:', result.data);
    
    if (result.statusCode === 200 && result.data?.status === 'ok') {
      console.log('✅ Node.js服务运行正常\n');
      return true;
    } else {
      console.log('❌ Node.js服务可能有问题\n');
      return false;
    }
  } catch (error) {
    console.error('❌ 健康检查失败:', error.message, '\n');
    return false;
  }
}

// 测试微信登录接口
async function testWechatLogin() {
  console.log('🔐 测试微信登录接口...\n');
  
  try {
    const result = await makeHttpsRequest(
      testConfig.domain, 
      '/api/auth/wechat-login', 
      'POST', 
      testConfig.testData
    );
    
    console.log('📊 微信登录测试结果:');
    console.log('   状态码:', result.statusCode);
    console.log('   响应数据:', result.data);
    console.log('   原始响应:', result.rawData);
    
    if (result.parseError) {
      console.log('   解析错误:', result.parseError);
    }
    
    // 分析结果
    if (result.statusCode === 200) {
      if (result.data?.success) {
        console.log('✅ 微信登录接口正常工作');
        console.log('   获得token:', result.data.data?.token ? '是' : '否');
      } else {
        console.log('⚠️  接口返回失败:', result.data?.message || '未知错误');
      }
    } else if (result.statusCode === 404) {
      console.log('❌ 接口不存在 - 可能是路由配置问题');
    } else if (result.statusCode === 500) {
      console.log('❌ 服务器内部错误');
    } else if (result.statusCode === 502) {
      console.log('❌ 网关错误 - Node.js服务可能未运行');
    } else {
      console.log('❌ 未知错误');
    }
    
    return result;
    
  } catch (error) {
    console.error('❌ 微信登录测试失败:', error.message);
    return null;
  }
}

// 测试其他API端点
async function testOtherEndpoints() {
  console.log('🔗 测试其他API端点...\n');
  
  const endpoints = [
    { path: '/api/payment/create', method: 'POST', needAuth: true },
    { path: '/api/payment/query/test123', method: 'GET', needAuth: true },
    { path: '/api/payment/verify', method: 'POST', needAuth: true },
    { path: '/api/payment/notify', method: 'POST', needAuth: false }
  ];
  
  for (const endpoint of endpoints) {
    try {
      console.log(`测试: ${endpoint.method} ${endpoint.path}`);
      
      const headers = endpoint.needAuth ? 
        { 'Authorization': 'Bearer test_token' } : {};
      
      const testData = endpoint.method === 'POST' ? 
        { test: true } : null;
      
      const result = await makeHttpsRequest(
        testConfig.domain, 
        endpoint.path, 
        endpoint.method, 
        testData,
        headers
      );
      
      console.log(`   状态码: ${result.statusCode}`);
      
      if (result.statusCode === 401 && endpoint.needAuth) {
        console.log('   ✅ 正确返回认证错误（符合预期）');
      } else if (result.statusCode === 404) {
        console.log('   ❌ 端点不存在');
      } else if (result.statusCode === 502) {
        console.log('   ❌ 网关错误');
      } else {
        console.log('   ✅ 端点可访问');
      }
      
    } catch (error) {
      console.log(`   ❌ 连接失败: ${error.message}`);
    }
    
    console.log('');
  }
}

// 主调试函数
async function debugDomainRequest() {
  console.log('🚀 开始调试域名请求...\n');
  console.log('🎯 目标域名:', testConfig.domain);
  console.log('📝 测试数据:', testConfig.testData);
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
  
  try {
    // 1. 测试健康检查
    const healthOk = await testHealthCheck();
    
    // 2. 测试微信登录接口
    const loginResult = await testWechatLogin();
    
    console.log('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
    
    // 3. 测试其他端点
    await testOtherEndpoints();
    
    // 4. 生成诊断报告
    console.log('📋 诊断报告:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    if (healthOk) {
      console.log('✅ Node.js服务运行正常');
    } else {
      console.log('❌ Node.js服务有问题');
    }
    
    if (loginResult) {
      if (loginResult.statusCode === 200) {
        console.log('✅ 微信登录接口可访问');
        if (loginResult.data?.success) {
          console.log('✅ 微信登录逻辑正常');
        } else {
          console.log('⚠️  微信登录逻辑需要检查');
        }
      } else {
        console.log('❌ 微信登录接口有问题');
      }
    }
    
    console.log('\n💡 建议:');
    if (!healthOk) {
      console.log('1. 检查Node.js服务是否在3002端口运行');
      console.log('2. 检查宝塔面板的反向代理配置');
      console.log('3. 查看Node.js服务日志');
    } else if (loginResult?.statusCode !== 200) {
      console.log('1. 检查API路由配置');
      console.log('2. 查看Node.js服务错误日志');
      console.log('3. 检查环境变量配置');
    } else if (!loginResult?.data?.success) {
      console.log('1. 检查微信小程序配置');
      console.log('2. 检查数据库连接');
      console.log('3. 查看详细错误信息');
    } else {
      console.log('🎉 域名访问配置正常！');
    }
    
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error);
  }
}

// 运行调试
if (require.main === module) {
  debugDomainRequest();
}

module.exports = {
  debugDomainRequest,
  testHealthCheck,
  testWechatLogin,
  makeHttpsRequest
};
