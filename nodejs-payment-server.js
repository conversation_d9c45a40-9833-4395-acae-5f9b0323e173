// Node.js支付服务器 - 完整版本
const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const axios = require('axios');

const app = express();
const PORT = process.env.PORT || 3002;

// 中间件配置
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 微信支付配置
const wechatPayConfig = {
  appid: 'wx996c8705b8b131e4',  // 您的小程序AppID
  mchid: '1722224105',          // 您的商户号
  private_key: `-----BEGIN PRIVATE KEY-----
MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDOZw1OfrunC3IQ
F0xAHyYzNN71YSH8xJe8a/I3Y2vvNwkq6L+oj1QQtUSkbxK0Y8ze5hxAHVPmSfZi
k8gW4c5tyu9uWZyoShbXFULC8pIbGhMNdw3qmWVuXYFTxoaULyE6WjVp5xgHUn4y
CjZkOch0uGNJgJ4ZXhOS5NXR3gYkUmkamaBPMfxHfOmZAaLLcDc0fEy5J6km7OsK
5/pWDnL7dxwMagPLR2CeYEixfPDRU7UcLEQOZvxEQb/hUYIxQGb08071rMwgvTws
ZnmqX6SAF6Yt7VfxTiNYJldYj6sxRRG8ql5Y+L2cLPgk/mYiOQgNLZOsG6vrt4dr
BaVCnlChAgMBAAECggEBAISj7QU4pFchH9f/YN0EJ4RYwb9nAHRz0h8gL7QwjDha
-----END PRIVATE KEY-----`,
  serial_no: '2E5B0C10CEA6BA89EF6C190808C0479CE2E67CC2',
  api_key: '15700526449td482471813td48247181'
};

// JWT密钥
const JWT_SECRET = 'your_jwt_secret_key_change_this_in_production';

// 内存存储（生产环境应使用数据库）
const orderStorage = new Map();
const userStorage = new Map();

// 认证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ success: false, message: '缺少访问令牌' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ success: false, message: '令牌无效' });
    }
    req.user = user;
    next();
  });
};

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'Node.js Payment Server'
  });
});

// 微信登录端点
app.post('/api/auth/wechat-login', async (req, res) => {
  try {
    const { code, userInfo } = req.body;
    
    console.log('微信登录请求:', { code: code ? '***' : null, userInfo });
    
    // 这里应该调用微信API获取openid
    // 暂时使用模拟数据
    const mockOpenid = `mock_openid_${Date.now()}`;
    
    // 生成JWT token
    const token = jwt.sign(
      { 
        openid: mockOpenid, 
        userInfo: userInfo,
        loginTime: Date.now()
      }, 
      JWT_SECRET, 
      { expiresIn: '24h' }
    );
    
    // 存储用户信息
    userStorage.set(mockOpenid, {
      openid: mockOpenid,
      userInfo: userInfo,
      points: 0,
      createTime: new Date()
    });
    
    console.log('微信登录成功:', mockOpenid);
    
    res.json({
      success: true,
      data: {
        token: token,
        expiresIn: 86400,
        openid: mockOpenid
      }
    });
  } catch (error) {
    console.error('微信登录失败:', error);
    res.status(500).json({
      success: false,
      message: '登录失败'
    });
  }
});

// 验证token端点
app.post('/api/auth/verify', authenticateToken, (req, res) => {
  res.json({
    success: true,
    data: {
      user: req.user,
      valid: true
    }
  });
});

// 创建支付订单端点
app.post('/api/payment/create', authenticateToken, async (req, res) => {
  try {
    const { amount, points, description } = req.body;
    const openid = req.user.openid;
    
    console.log('创建支付订单:', { amount, points, description, openid });
    
    // 生成订单号
    const out_trade_no = generateOrderNo();
    
    // 构建微信支付订单数据
    const orderData = {
      appid: wechatPayConfig.appid,
      mchid: wechatPayConfig.mchid,
      description: description || `积分充值${points}积分`,
      out_trade_no: out_trade_no,
      notify_url: 'https://chendumiaomu.asia/api/payment/notify',  // 回调URL
      amount: {
        total: Math.floor(amount * 100), // 转换为分
        currency: 'CNY'
      },
      payer: {
        openid: openid
      }
    };
    
    // 存储订单信息
    orderStorage.set(out_trade_no, {
      ...orderData,
      points: points,
      status: 'pending',
      createTime: new Date()
    });
    
    // 调用微信支付API创建订单
    const payResult = await createWechatPayOrder(orderData);
    
    // 生成小程序支付参数
    const payParams = generateMiniProgramPayParams(payResult.prepay_id);
    
    console.log('支付订单创建成功:', out_trade_no);
    
    res.json({
      success: true,
      data: {
        out_trade_no: out_trade_no,
        ...payParams
      }
    });
    
  } catch (error) {
    console.error('创建支付订单失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '创建订单失败'
    });
  }
});

// 🔥 关键：微信支付回调端点
app.post('/api/payment/notify', async (req, res) => {
  try {
    console.log('收到微信支付回调:', {
      headers: req.headers,
      body: req.body
    });
    
    // 获取微信支付回调头信息
    const signature = req.headers['wechatpay-signature'];
    const timestamp = req.headers['wechatpay-timestamp'];
    const nonce = req.headers['wechatpay-nonce'];
    const serial = req.headers['wechatpay-serial'];
    
    // 验证签名（简化版本，生产环境需要完整验证）
    if (!verifyWechatPaySignature(timestamp, nonce, req.body, signature)) {
      console.error('微信支付回调签名验证失败');
      return res.status(401).json({ code: 'FAIL', message: '签名验证失败' });
    }
    
    // 解析回调数据
    const callbackData = req.body;
    
    if (callbackData.event_type === 'TRANSACTION.SUCCESS') {
      // 解密支付数据
      const resource = callbackData.resource;
      const orderData = decryptWechatPayData(
        resource.ciphertext,
        resource.associated_data,
        resource.nonce
      );
      
      // 处理支付成功
      const success = await handlePaymentSuccess(orderData);
      
      if (success) {
        console.log('支付回调处理成功:', orderData.out_trade_no);
        res.json({ code: 'SUCCESS', message: '处理成功' });
      } else {
        console.error('支付回调处理失败:', orderData.out_trade_no);
        res.status(500).json({ code: 'FAIL', message: '处理失败' });
      }
    } else {
      console.log('忽略非支付成功事件:', callbackData.event_type);
      res.json({ code: 'SUCCESS', message: '忽略事件' });
    }
    
  } catch (error) {
    console.error('支付回调处理异常:', error);
    res.status(500).json({ code: 'FAIL', message: error.message });
  }
});

// 查询支付状态端点
app.post('/api/payment/query', authenticateToken, async (req, res) => {
  try {
    const { orderNo } = req.body;
    
    // 从本地存储查询订单
    const order = orderStorage.get(orderNo);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }
    
    // 也可以调用微信支付查询API获取最新状态
    // const paymentInfo = await queryWechatPayOrder(orderNo);
    
    res.json({
      success: true,
      data: {
        out_trade_no: orderNo,
        status: order.status,
        amount: order.amount,
        points: order.points
      }
    });
    
  } catch (error) {
    console.error('查询支付状态失败:', error);
    res.status(500).json({
      success: false,
      message: '查询失败'
    });
  }
});

// 验证支付结果端点
app.post('/api/payment/verify', authenticateToken, async (req, res) => {
  try {
    const { orderNo, expectedPoints } = req.body;
    const openid = req.user.openid;
    
    // 检查订单状态
    const order = orderStorage.get(orderNo);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }
    
    // 检查用户积分是否已更新
    const user = userStorage.get(openid);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }
    
    res.json({
      success: true,
      data: {
        verified: order.status === 'completed',
        currentPoints: user.points,
        orderStatus: order.status
      }
    });
    
  } catch (error) {
    console.error('验证支付结果失败:', error);
    res.status(500).json({
      success: false,
      message: '验证失败'
    });
  }
});

// 工具函数
function generateOrderNo() {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `NODE${timestamp}${random}`;
}

function generateMiniProgramPayParams(prepay_id) {
  const timeStamp = Math.floor(Date.now() / 1000).toString();
  const nonceStr = crypto.randomBytes(16).toString('hex');
  const package_str = `prepay_id=${prepay_id}`;
  
  // 生成签名
  const signStr = `${wechatPayConfig.appid}\n${timeStamp}\n${nonceStr}\n${package_str}\n`;
  const paySign = crypto.sign('RSA-SHA256', Buffer.from(signStr), wechatPayConfig.private_key).toString('base64');
  
  return {
    timeStamp: timeStamp,
    nonceStr: nonceStr,
    package: package_str,
    signType: 'RSA',
    paySign: paySign
  };
}

async function createWechatPayOrder(orderData) {
  // 模拟微信支付API调用
  // 实际实现需要调用微信支付API
  console.log('模拟调用微信支付API:', orderData);
  
  return {
    prepay_id: `wx_prepay_${Date.now()}`
  };
}

function verifyWechatPaySignature(timestamp, nonce, body, signature) {
  // 简化的签名验证，生产环境需要使用微信支付平台证书
  console.log('验证微信支付签名:', { timestamp, nonce, signature });
  return true; // 暂时返回true
}

function decryptWechatPayData(ciphertext, associated_data, nonce) {
  // 简化的解密，生产环境需要使用AES-256-GCM解密
  console.log('解密微信支付数据:', { ciphertext, associated_data, nonce });
  
  // 模拟解密后的数据
  return {
    out_trade_no: 'mock_order_' + Date.now(),
    transaction_id: 'wx_trans_' + Date.now(),
    payer: { openid: 'mock_openid' },
    amount: { total: 100 } // 1元，单位分
  };
}

async function handlePaymentSuccess(orderData) {
  try {
    const { out_trade_no, transaction_id, payer, amount } = orderData;
    
    console.log('处理支付成功:', { out_trade_no, transaction_id, openid: payer.openid });
    
    // 获取订单信息
    const order = orderStorage.get(out_trade_no);
    if (!order) {
      console.error('订单不存在:', out_trade_no);
      return false;
    }
    
    // 防止重复处理
    if (order.status === 'completed') {
      console.log('订单已处理过:', out_trade_no);
      return true;
    }
    
    // 更新用户积分
    const user = userStorage.get(payer.openid);
    if (user) {
      user.points += order.points;
      userStorage.set(payer.openid, user);
    }
    
    // 更新订单状态
    order.status = 'completed';
    order.transaction_id = transaction_id;
    order.completeTime = new Date();
    orderStorage.set(out_trade_no, order);
    
    console.log(`支付处理完成: ${out_trade_no}, 用户${payer.openid}获得${order.points}积分`);
    return true;
    
  } catch (error) {
    console.error('处理支付成功失败:', error);
    return false;
  }
}

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 Node.js支付服务器启动成功！`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🌐 生产地址: https://chendumiaomu.asia`);
  console.log(`💰 支付回调地址: https://chendumiaomu.asia/api/payment/notify`);
  console.log(`🔍 健康检查: https://chendumiaomu.asia/health`);
});

module.exports = app;
