// 在小程序中测试域名访问的代码
// 可以在小程序的任意页面中运行这段代码进行测试

/**
 * 测试域名访问功能
 * 在小程序页面的 onLoad 或按钮点击事件中调用
 */
function testDomainAccess() {
  console.log('🧪 开始测试域名访问...');
  
  // 测试配置
  const testConfig = {
    baseUrl: 'https://chendumiaomu.asia',
    endpoints: [
      { path: '/health', method: 'GET', name: '健康检查' },
      { path: '/api/auth/wechat-login', method: 'POST', name: '微信登录' }
    ]
  };
  
  // 测试健康检查
  testHealthCheck(testConfig.baseUrl);
  
  // 延迟测试API接口
  setTimeout(() => {
    testWechatLoginAPI(testConfig.baseUrl);
  }, 2000);
}

/**
 * 测试健康检查端点
 */
function testHealthCheck(baseUrl) {
  console.log('🏥 测试健康检查端点...');
  
  wx.request({
    url: `${baseUrl}/health`,
    method: 'GET',
    timeout: 10000,
    success: (res) => {
      console.log('✅ 健康检查成功:', {
        statusCode: res.statusCode,
        data: res.data,
        timestamp: new Date().toISOString()
      });
      
      if (res.statusCode === 200 && res.data?.status === 'ok') {
        wx.showToast({
          title: '域名访问正常',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: '服务响应异常',
          icon: 'none'
        });
      }
    },
    fail: (error) => {
      console.error('❌ 健康检查失败:', {
        error,
        errorMsg: error.errMsg,
        timestamp: new Date().toISOString()
      });
      
      // 分析错误原因
      analyzeRequestError(error, '健康检查');
    }
  });
}

/**
 * 测试微信登录API
 */
function testWechatLoginAPI(baseUrl) {
  console.log('🔐 测试微信登录API...');
  
  // 模拟登录数据
  const testData = {
    code: 'test_code_' + Date.now(),
    userInfo: {
      nickName: '测试用户',
      avatarUrl: 'https://example.com/avatar.jpg',
      gender: 1,
      country: '中国',
      province: '广东',
      city: '深圳'
    }
  };
  
  wx.request({
    url: `${baseUrl}/api/auth/wechat-login`,
    method: 'POST',
    data: testData,
    header: {
      'Content-Type': 'application/json'
    },
    timeout: 15000,
    success: (res) => {
      console.log('✅ 微信登录API测试成功:', {
        statusCode: res.statusCode,
        data: res.data,
        timestamp: new Date().toISOString()
      });
      
      if (res.statusCode === 200) {
        if (res.data?.success) {
          wx.showToast({
            title: 'API调用成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: res.data?.message || 'API返回错误',
            icon: 'none'
          });
        }
      } else {
        wx.showToast({
          title: `服务器错误: ${res.statusCode}`,
          icon: 'none'
        });
      }
    },
    fail: (error) => {
      console.error('❌ 微信登录API测试失败:', {
        error,
        errorMsg: error.errMsg,
        timestamp: new Date().toISOString()
      });
      
      // 分析错误原因
      analyzeRequestError(error, '微信登录API');
    }
  });
}

/**
 * 分析请求错误原因
 */
function analyzeRequestError(error, apiName) {
  console.log(`🔍 分析 ${apiName} 错误原因:`);
  
  const errorMsg = error.errMsg || '';
  let diagnosis = '';
  let solution = '';
  
  if (errorMsg.includes('request:fail url not in domain list')) {
    diagnosis = '域名不在小程序白名单中';
    solution = '需要在微信小程序后台添加域名到白名单';
    
    console.error('🚨 诊断结果: 域名白名单问题');
    console.error('📝 解决方案:');
    console.error('1. 登录微信小程序后台 (mp.weixin.qq.com)');
    console.error('2. 开发管理 → 开发设置 → 服务器域名');
    console.error('3. 在 request合法域名 中添加: https://chendumiaomu.asia');
    console.error('4. 保存并提交，等待生效');
    
    wx.showModal({
      title: '域名访问失败',
      content: '域名未添加到小程序白名单，请联系管理员配置',
      showCancel: false
    });
    
  } else if (errorMsg.includes('request:fail ssl hand shake error')) {
    diagnosis = 'SSL证书问题';
    solution = '检查SSL证书配置';
    
    console.error('🚨 诊断结果: SSL证书问题');
    console.error('📝 解决方案: 检查域名SSL证书是否正确配置');
    
    wx.showModal({
      title: 'SSL证书错误',
      content: 'HTTPS证书配置有问题，请联系技术人员',
      showCancel: false
    });
    
  } else if (errorMsg.includes('request:fail timeout')) {
    diagnosis = '请求超时';
    solution = '检查网络连接和服务器响应';
    
    console.error('🚨 诊断结果: 请求超时');
    console.error('📝 解决方案: 检查网络连接和服务器状态');
    
    wx.showToast({
      title: '网络超时，请重试',
      icon: 'none'
    });
    
  } else if (errorMsg.includes('request:fail')) {
    diagnosis = '网络请求失败';
    solution = '检查网络连接';
    
    console.error('🚨 诊断结果: 网络请求失败');
    console.error('📝 完整错误信息:', error);
    
    wx.showToast({
      title: '网络请求失败',
      icon: 'none'
    });
    
  } else {
    diagnosis = '未知错误';
    solution = '查看详细日志';
    
    console.error('🚨 诊断结果: 未知错误');
    console.error('📝 完整错误信息:', error);
  }
  
  // 记录诊断结果
  console.log('📊 错误诊断报告:', {
    apiName,
    errorMsg,
    diagnosis,
    solution,
    timestamp: new Date().toISOString()
  });
}

/**
 * 测试开发者工具设置
 */
function checkDevToolSettings() {
  console.log('🔧 检查开发者工具设置...');
  console.log('💡 如果域名访问失败，请检查:');
  console.log('1. 微信开发者工具 → 右上角详情 → 本地设置');
  console.log('2. 勾选 "不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"');
  console.log('3. 重新编译项目');
}

/**
 * 一键测试所有功能
 */
function runAllTests() {
  console.log('🚀 开始全面测试域名访问功能...');
  
  // 检查开发者工具设置提示
  checkDevToolSettings();
  
  // 延迟执行测试，给用户时间查看提示
  setTimeout(() => {
    testDomainAccess();
  }, 1000);
}

// 导出测试函数，可以在页面中调用
module.exports = {
  testDomainAccess,
  testHealthCheck,
  testWechatLoginAPI,
  analyzeRequestError,
  checkDevToolSettings,
  runAllTests
};

// 使用示例：
// 在页面的 onLoad 中调用：
// const domainTest = require('../../test-domain-in-miniprogram.js');
// domainTest.runAllTests();
