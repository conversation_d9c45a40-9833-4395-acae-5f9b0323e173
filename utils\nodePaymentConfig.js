/**
 * Node.js支付服务配置
 */

// 临时使用IP地址（域名问题解决后改回域名）
const getApiBaseUrl = () => {
  const apiUrl = 'http://************:3002';
  console.log('使用API地址:', apiUrl);
  return apiUrl;
};

const config = {
  // API基础地址
  baseUrl: getApiBaseUrl(),
  
  // API端点
  endpoints: {
    // 认证相关
    wechatLogin: '/api/auth/wechat-login',
    refreshToken: '/api/auth/refresh-token',
    verifyToken: '/api/auth/verify',
    
    // 支付相关
    createPayment: '/api/payment/create',
    queryPayment: '/api/payment/query',
    verifyPayment: '/api/payment/verify'
  },
  
  // 请求配置
  request: {
    timeout: 30000, // 30秒超时
    header: {
      'Content-Type': 'application/json'
    }
  }
};

/**
 * 获取完整的API URL
 * @param {string} endpoint 端点名称
 * @returns {string} 完整URL
 */
const getApiUrl = (endpoint) => {
  if (!config.endpoints[endpoint]) {
    throw new Error(`Unknown endpoint: ${endpoint}`);
  }
  return config.baseUrl + config.endpoints[endpoint];
};

/**
 * 发起API请求的通用方法
 * @param {string} endpoint 端点名称
 * @param {object} options 请求选项
 * @returns {Promise} 请求结果
 */
const apiRequest = (endpoint, options = {}) => {
  const url = getApiUrl(endpoint);
  
  const requestOptions = {
    url: url,
    timeout: config.request.timeout,
    header: {
      ...config.request.header,
      ...options.header
    },
    method: options.method || 'GET',
    data: options.data || {}
  };

  console.log('Node.js API Request:', {
    endpoint,
    url,
    method: requestOptions.method,
    data: requestOptions.data
  });

  return new Promise((resolve, reject) => {
    wx.request({
      ...requestOptions,
      success: (res) => {
        console.log('Node.js API Response:', {
          endpoint,
          statusCode: res.statusCode,
          data: res.data
        });
        
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(new Error(`API request failed with status ${res.statusCode}`));
        }
      },
      fail: (error) => {
        console.error('Node.js API Request Failed:', {
          endpoint,
          error
        });
        reject(error);
      }
    });
  });
};

/**
 * 带认证的API请求
 * @param {string} endpoint 端点名称
 * @param {string} token JWT token
 * @param {object} options 请求选项
 * @returns {Promise} 请求结果
 */
const authenticatedRequest = (endpoint, token, options = {}) => {
  const authOptions = {
    ...options,
    header: {
      ...options.header,
      'Authorization': `Bearer ${token}`
    }
  };
  
  return apiRequest(endpoint, authOptions);
};

module.exports = {
  config,
  getApiUrl,
  apiRequest,
  authenticatedRequest
};
