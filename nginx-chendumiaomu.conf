# Nginx配置文件 - chendumiaomu.asia
# 保存到: /etc/nginx/sites-available/chendumiaomu.asia

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name chendumiaomu.asia www.chendumiaomu.asia;
    
    # 重定向所有HTTP请求到HTTPS
    return 301 https://$server_name$request_uri;
}

# HTTPS主配置
server {
    listen 443 ssl http2;
    server_name chendumiaomu.asia www.chendumiaomu.asia;
    
    # SSL证书配置
    ssl_certificate /etc/letsencrypt/live/chendumiaomu.asia/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/chendumiaomu.asia/privkey.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-CHACHA20-POLY1305;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头设置
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 日志配置
    access_log /var/log/nginx/chendumiaomu_access.log;
    error_log /var/log/nginx/chendumiaomu_error.log;
    
    # 🔥 关键：API路由代理到Node.js服务
    location /api/ {
        proxy_pass http://127.0.0.1:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 支持大文件上传
        client_max_body_size 10M;
    }
    
    # 健康检查端点
    location /health {
        proxy_pass http://127.0.0.1:3002/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        access_log off;
    }
    
    # 根路径处理
    location / {
        # 可以返回一个简单的状态页面
        return 200 '{"status":"ok","service":"Payment Server","timestamp":"$time_iso8601"}';
        add_header Content-Type application/json;
    }
    
    # 静态文件缓存（如果有的话）
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 防止访问敏感文件
    location ~ /\. {
        deny all;
    }
    
    location ~ /(logs|node_modules|src)/ {
        deny all;
    }
}

# 如果需要支持其他子域名
server {
    listen 443 ssl http2;
    server_name *.chendumiaomu.asia;
    
    ssl_certificate /etc/letsencrypt/live/chendumiaomu.asia/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/chendumiaomu.asia/privkey.pem;
    
    # 重定向到主域名
    return 301 https://chendumiaomu.asia$request_uri;
}
