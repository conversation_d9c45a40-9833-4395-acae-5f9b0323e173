#!/bin/bash

# 域名访问配置脚本
# 用于配置 chendumiaomu.asia 域名访问Node.js支付服务

echo "🚀 开始配置域名访问..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 域名和服务配置
DOMAIN="chendumiaomu.asia"
SERVER_IP="***********"
NODE_PORT="3002"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}请使用root权限运行此脚本${NC}"
    echo "使用: sudo bash setup-domain-access.sh"
    exit 1
fi

echo -e "${GREEN}1. 检查域名解析...${NC}"
# 检查域名解析
RESOLVED_IP=$(dig +short $DOMAIN | tail -n1)
if [ "$RESOLVED_IP" = "$SERVER_IP" ]; then
    echo -e "${GREEN}✅ 域名解析正确: $DOMAIN -> $SERVER_IP${NC}"
else
    echo -e "${YELLOW}⚠️  域名解析检查:${NC}"
    echo "   期望IP: $SERVER_IP"
    echo "   实际IP: $RESOLVED_IP"
    echo -e "${YELLOW}   请确保域名DNS记录正确配置${NC}"
fi

echo -e "${GREEN}2. 检查Node.js服务状态...${NC}"
# 检查Node.js服务是否运行
if curl -s http://localhost:$NODE_PORT/health > /dev/null; then
    echo -e "${GREEN}✅ Node.js服务运行正常 (端口:$NODE_PORT)${NC}"
else
    echo -e "${RED}❌ Node.js服务未运行，请先启动服务${NC}"
    echo "启动命令: cd payment-server && npm start"
    exit 1
fi

echo -e "${GREEN}3. 安装Nginx...${NC}"
# 安装Nginx
if ! command -v nginx &> /dev/null; then
    apt update
    apt install -y nginx
    echo -e "${GREEN}✅ Nginx安装完成${NC}"
else
    echo -e "${GREEN}✅ Nginx已安装${NC}"
fi

echo -e "${GREEN}4. 配置Nginx...${NC}"
# 复制Nginx配置文件
if [ -f "nginx-chendumiaomu.conf" ]; then
    cp nginx-chendumiaomu.conf /etc/nginx/sites-available/$DOMAIN
    
    # 创建软链接启用站点
    ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/
    
    # 删除默认站点（如果存在）
    rm -f /etc/nginx/sites-enabled/default
    
    echo -e "${GREEN}✅ Nginx配置文件已部署${NC}"
else
    echo -e "${RED}❌ 找不到nginx-chendumiaomu.conf文件${NC}"
    exit 1
fi

echo -e "${GREEN}5. 测试Nginx配置...${NC}"
# 测试Nginx配置
if nginx -t; then
    echo -e "${GREEN}✅ Nginx配置测试通过${NC}"
else
    echo -e "${RED}❌ Nginx配置有误${NC}"
    exit 1
fi

echo -e "${GREEN}6. 安装SSL证书...${NC}"
# 安装Certbot
if ! command -v certbot &> /dev/null; then
    apt install -y certbot python3-certbot-nginx
    echo -e "${GREEN}✅ Certbot安装完成${NC}"
else
    echo -e "${GREEN}✅ Certbot已安装${NC}"
fi

# 申请SSL证书
echo -e "${YELLOW}正在申请SSL证书...${NC}"
echo -e "${YELLOW}注意：这需要域名正确解析到此服务器${NC}"

# 先启动Nginx（临时配置，用于证书验证）
systemctl start nginx

# 申请证书
if certbot --nginx -d $DOMAIN --non-interactive --agree-tos --email admin@$DOMAIN; then
    echo -e "${GREEN}✅ SSL证书申请成功${NC}"
else
    echo -e "${YELLOW}⚠️  SSL证书申请失败，将使用HTTP配置${NC}"
    
    # 创建HTTP版本的配置
    cat > /etc/nginx/sites-available/$DOMAIN << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    
    location /api/ {
        proxy_pass http://127.0.0.1:$NODE_PORT;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    location /health {
        proxy_pass http://127.0.0.1:$NODE_PORT/health;
        proxy_set_header Host \$host;
    }
    
    location / {
        return 200 '{"status":"ok","service":"Payment Server HTTP"}';
        add_header Content-Type application/json;
    }
}
EOF
fi

echo -e "${GREEN}7. 启动服务...${NC}"
# 重启Nginx
systemctl restart nginx
systemctl enable nginx

# 配置防火墙
echo -e "${GREEN}8. 配置防火墙...${NC}"
ufw allow 22
ufw allow 80
ufw allow 443
ufw --force enable

echo -e "${GREEN}9. 测试域名访问...${NC}"
# 测试域名访问
sleep 3

echo "测试健康检查端点..."
if curl -s https://$DOMAIN/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ HTTPS访问正常: https://$DOMAIN/health${NC}"
    PROTOCOL="https"
elif curl -s http://$DOMAIN/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ HTTP访问正常: http://$DOMAIN/health${NC}"
    PROTOCOL="http"
else
    echo -e "${RED}❌ 域名访问测试失败${NC}"
    echo "请检查："
    echo "1. 域名DNS解析是否正确"
    echo "2. 防火墙是否允许80/443端口"
    echo "3. Node.js服务是否正常运行"
    exit 1
fi

echo -e "${GREEN}🎉 域名访问配置完成！${NC}"
echo ""
echo "📋 配置信息："
echo "   域名: $DOMAIN"
echo "   协议: $PROTOCOL"
echo "   Node.js端口: $NODE_PORT"
echo ""
echo "🔗 访问地址："
echo "   健康检查: $PROTOCOL://$DOMAIN/health"
echo "   API基础地址: $PROTOCOL://$DOMAIN/api/"
echo ""
echo "📝 下一步："
echo "1. 更新小程序中的API地址为: $PROTOCOL://$DOMAIN"
echo "2. 在微信支付商户平台配置回调URL: $PROTOCOL://$DOMAIN/api/payment/notify"
echo "3. 测试支付功能"

# 显示服务状态
echo ""
echo -e "${GREEN}📊 服务状态：${NC}"
echo "Nginx状态: $(systemctl is-active nginx)"
echo "Node.js服务: $(curl -s http://localhost:$NODE_PORT/health | grep -o '"status":"ok"' || echo '未响应')"

echo ""
echo -e "${GREEN}✨ 配置完成！${NC}"
